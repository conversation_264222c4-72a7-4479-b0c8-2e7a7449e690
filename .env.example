# Docker Compose 环境变量配置文件
# 复制此文件为 .env 并修改相应的值

# Redis 配置
# REDIS_PASSWORD=  # 无密码配置
REDIS_PORT=6379

# PostgreSQL 配置
POSTGRES_DB=myapp
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
POSTGRES_PORT=5432

# MySQL 配置（如果启用）
MYSQL_ROOT_PASSWORD=mysql123
MYSQL_DATABASE=myapp
MYSQL_USER=appuser
MYSQL_PASSWORD=apppass
MYSQL_PORT=3306

# MongoDB 配置（如果启用）
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=mongo123
MONGO_INITDB_DATABASE=myapp
MONGO_PORT=27017

# 网络配置
NETWORK_NAME=database-network

# 数据卷配置
REDIS_DATA_VOLUME=redis_data
POSTGRES_DATA_VOLUME=postgres_data
MYSQL_DATA_VOLUME=mysql_data
MONGO_DATA_VOLUME=mongodb_data
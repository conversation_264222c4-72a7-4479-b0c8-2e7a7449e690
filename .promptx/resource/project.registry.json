{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-31T02:16:42.152Z", "updatedAt": "2025-07-31T02:16:42.164Z", "resourceCount": 19}, "resources": [{"id": "movement-description-workflow", "source": "project", "protocol": "execution", "name": "Movement Description Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/movement-expert/execution/movement-description-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T02:16:42.156Z", "updatedAt": "2025-07-31T02:16:42.156Z", "scannedAt": "2025-07-31T02:16:42.156Z", "path": "role/movement-expert/execution/movement-description-workflow.execution.md"}}, {"id": "movement-expert", "source": "project", "protocol": "role", "name": "Movement Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/movement-expert/movement-expert.role.md", "metadata": {"createdAt": "2025-07-31T02:16:42.156Z", "updatedAt": "2025-07-31T02:16:42.156Z", "scannedAt": "2025-07-31T02:16:42.156Z", "path": "role/movement-expert/movement-expert.role.md"}}, {"id": "biomechanical-analysis", "source": "project", "protocol": "thought", "name": "Biomechanical Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/movement-expert/thought/biomechanical-analysis.thought.md", "metadata": {"createdAt": "2025-07-31T02:16:42.157Z", "updatedAt": "2025-07-31T02:16:42.157Z", "scannedAt": "2025-07-31T02:16:42.157Z", "path": "role/movement-expert/thought/biomechanical-analysis.thought.md"}}, {"id": "creative-process", "source": "project", "protocol": "execution", "name": "Creative Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/novel-master/execution/creative-process.execution.md", "metadata": {"createdAt": "2025-07-31T02:16:42.158Z", "updatedAt": "2025-07-31T02:16:42.158Z", "scannedAt": "2025-07-31T02:16:42.158Z", "path": "role/novel-master/execution/creative-process.execution.md"}}, {"id": "story-development", "source": "project", "protocol": "execution", "name": "Story Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/novel-master/execution/story-development.execution.md", "metadata": {"createdAt": "2025-07-31T02:16:42.158Z", "updatedAt": "2025-07-31T02:16:42.158Z", "scannedAt": "2025-07-31T02:16:42.158Z", "path": "role/novel-master/execution/story-development.execution.md"}}, {"id": "novel-master", "source": "project", "protocol": "role", "name": "Novel Master 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/novel-master/novel-master.role.md", "metadata": {"createdAt": "2025-07-31T02:16:42.158Z", "updatedAt": "2025-07-31T02:16:42.158Z", "scannedAt": "2025-07-31T02:16:42.158Z", "path": "role/novel-master/novel-master.role.md"}}, {"id": "creative-thinking", "source": "project", "protocol": "thought", "name": "Creative Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/novel-master/thought/creative-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T02:16:42.159Z", "updatedAt": "2025-07-31T02:16:42.159Z", "scannedAt": "2025-07-31T02:16:42.159Z", "path": "role/novel-master/thought/creative-thinking.thought.md"}}, {"id": "narrative-structure", "source": "project", "protocol": "thought", "name": "Narrative Structure 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/novel-master/thought/narrative-structure.thought.md", "metadata": {"createdAt": "2025-07-31T02:16:42.159Z", "updatedAt": "2025-07-31T02:16:42.159Z", "scannedAt": "2025-07-31T02:16:42.159Z", "path": "role/novel-master/thought/narrative-structure.thought.md"}}, {"id": "prompt-optimization", "source": "project", "protocol": "execution", "name": "Prompt Optimization 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/prompt-architect/execution/prompt-optimization.execution.md", "metadata": {"createdAt": "2025-07-31T02:16:42.159Z", "updatedAt": "2025-07-31T02:16:42.159Z", "scannedAt": "2025-07-31T02:16:42.159Z", "path": "role/prompt-architect/execution/prompt-optimization.execution.md"}}, {"id": "prompt-architect", "source": "project", "protocol": "role", "name": "Prompt Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/prompt-architect/prompt-architect.role.md", "metadata": {"createdAt": "2025-07-31T02:16:42.160Z", "updatedAt": "2025-07-31T02:16:42.160Z", "scannedAt": "2025-07-31T02:16:42.160Z", "path": "role/prompt-architect/prompt-architect.role.md"}}, {"id": "structured-thinking", "source": "project", "protocol": "thought", "name": "Structured Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/prompt-architect/thought/structured-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T02:16:42.160Z", "updatedAt": "2025-07-31T02:16:42.160Z", "scannedAt": "2025-07-31T02:16:42.160Z", "path": "role/prompt-architect/thought/structured-thinking.thought.md"}}, {"id": "prompt-optimization-workflow", "source": "project", "protocol": "execution", "name": "Prompt Optimization Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/prompt-video-expert/execution/prompt-optimization-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T02:16:42.161Z", "updatedAt": "2025-07-31T02:16:42.161Z", "scannedAt": "2025-07-31T02:16:42.161Z", "path": "role/prompt-video-expert/execution/prompt-optimization-workflow.execution.md"}}, {"id": "prompt-video-expert", "source": "project", "protocol": "role", "name": "Prompt Video Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/prompt-video-expert/prompt-video-expert.role.md", "metadata": {"createdAt": "2025-07-31T02:16:42.161Z", "updatedAt": "2025-07-31T02:16:42.161Z", "scannedAt": "2025-07-31T02:16:42.161Z", "path": "role/prompt-video-expert/prompt-video-expert.role.md"}}, {"id": "creative-visual-thinking", "source": "project", "protocol": "thought", "name": "Creative Visual Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/prompt-video-expert/thought/creative-visual-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T02:16:42.161Z", "updatedAt": "2025-07-31T02:16:42.161Z", "scannedAt": "2025-07-31T02:16:42.161Z", "path": "role/prompt-video-expert/thought/creative-visual-thinking.thought.md"}}, {"id": "requirement-analysis", "source": "project", "protocol": "execution", "name": "Requirement Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/tdd-expert/execution/requirement-analysis.execution.md", "metadata": {"createdAt": "2025-07-31T02:16:42.162Z", "updatedAt": "2025-07-31T02:16:42.162Z", "scannedAt": "2025-07-31T02:16:42.162Z", "path": "role/tdd-expert/execution/requirement-analysis.execution.md"}}, {"id": "tdd-workflow", "source": "project", "protocol": "execution", "name": "Tdd Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/tdd-expert/execution/tdd-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T02:16:42.163Z", "updatedAt": "2025-07-31T02:16:42.163Z", "scannedAt": "2025-07-31T02:16:42.163Z", "path": "role/tdd-expert/execution/tdd-workflow.execution.md"}}, {"id": "tdd-expert", "source": "project", "protocol": "role", "name": "Tdd Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/tdd-expert/tdd-expert.role.md", "metadata": {"createdAt": "2025-07-31T02:16:42.163Z", "updatedAt": "2025-07-31T02:16:42.163Z", "scannedAt": "2025-07-31T02:16:42.163Z", "path": "role/tdd-expert/tdd-expert.role.md"}}, {"id": "critical-thinking", "source": "project", "protocol": "thought", "name": "Critical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/tdd-expert/thought/critical-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T02:16:42.164Z", "updatedAt": "2025-07-31T02:16:42.164Z", "scannedAt": "2025-07-31T02:16:42.164Z", "path": "role/tdd-expert/thought/critical-thinking.thought.md"}}, {"id": "technical-insight", "source": "project", "protocol": "thought", "name": "Technical Insight 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/tdd-expert/thought/technical-insight.thought.md", "metadata": {"createdAt": "2025-07-31T02:16:42.164Z", "updatedAt": "2025-07-31T02:16:42.164Z", "scannedAt": "2025-07-31T02:16:42.164Z", "path": "role/tdd-expert/thought/technical-insight.thought.md"}}], "stats": {"totalResources": 19, "byProtocol": {"execution": 7, "role": 5, "thought": 7}, "bySource": {"project": 19}}}
{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-31T01:44:55.296Z", "updatedAt": "2025-07-31T01:44:55.300Z", "resourceCount": 11}, "resources": [{"id": "movement-description-workflow", "source": "project", "protocol": "execution", "name": "Movement Description Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/movement-expert/execution/movement-description-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T01:44:55.297Z", "updatedAt": "2025-07-31T01:44:55.297Z", "scannedAt": "2025-07-31T01:44:55.297Z", "path": "role/movement-expert/execution/movement-description-workflow.execution.md"}}, {"id": "movement-expert", "source": "project", "protocol": "role", "name": "Movement Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/movement-expert/movement-expert.role.md", "metadata": {"createdAt": "2025-07-31T01:44:55.297Z", "updatedAt": "2025-07-31T01:44:55.297Z", "scannedAt": "2025-07-31T01:44:55.297Z", "path": "role/movement-expert/movement-expert.role.md"}}, {"id": "biomechanical-analysis", "source": "project", "protocol": "thought", "name": "Biomechanical Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/movement-expert/thought/biomechanical-analysis.thought.md", "metadata": {"createdAt": "2025-07-31T01:44:55.297Z", "updatedAt": "2025-07-31T01:44:55.297Z", "scannedAt": "2025-07-31T01:44:55.297Z", "path": "role/movement-expert/thought/biomechanical-analysis.thought.md"}}, {"id": "prompt-optimization-workflow", "source": "project", "protocol": "execution", "name": "Prompt Optimization Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/prompt-video-expert/execution/prompt-optimization-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T01:44:55.298Z", "updatedAt": "2025-07-31T01:44:55.298Z", "scannedAt": "2025-07-31T01:44:55.298Z", "path": "role/prompt-video-expert/execution/prompt-optimization-workflow.execution.md"}}, {"id": "prompt-video-expert", "source": "project", "protocol": "role", "name": "Prompt Video Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/prompt-video-expert/prompt-video-expert.role.md", "metadata": {"createdAt": "2025-07-31T01:44:55.298Z", "updatedAt": "2025-07-31T01:44:55.298Z", "scannedAt": "2025-07-31T01:44:55.298Z", "path": "role/prompt-video-expert/prompt-video-expert.role.md"}}, {"id": "creative-visual-thinking", "source": "project", "protocol": "thought", "name": "Creative Visual Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/prompt-video-expert/thought/creative-visual-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T01:44:55.298Z", "updatedAt": "2025-07-31T01:44:55.298Z", "scannedAt": "2025-07-31T01:44:55.298Z", "path": "role/prompt-video-expert/thought/creative-visual-thinking.thought.md"}}, {"id": "requirement-analysis", "source": "project", "protocol": "execution", "name": "Requirement Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/tdd-expert/execution/requirement-analysis.execution.md", "metadata": {"createdAt": "2025-07-31T01:44:55.298Z", "updatedAt": "2025-07-31T01:44:55.298Z", "scannedAt": "2025-07-31T01:44:55.298Z", "path": "role/tdd-expert/execution/requirement-analysis.execution.md"}}, {"id": "tdd-workflow", "source": "project", "protocol": "execution", "name": "Tdd Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/tdd-expert/execution/tdd-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T01:44:55.299Z", "updatedAt": "2025-07-31T01:44:55.299Z", "scannedAt": "2025-07-31T01:44:55.299Z", "path": "role/tdd-expert/execution/tdd-workflow.execution.md"}}, {"id": "tdd-expert", "source": "project", "protocol": "role", "name": "Tdd Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/tdd-expert/tdd-expert.role.md", "metadata": {"createdAt": "2025-07-31T01:44:55.299Z", "updatedAt": "2025-07-31T01:44:55.299Z", "scannedAt": "2025-07-31T01:44:55.299Z", "path": "role/tdd-expert/tdd-expert.role.md"}}, {"id": "critical-thinking", "source": "project", "protocol": "thought", "name": "Critical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/tdd-expert/thought/critical-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T01:44:55.299Z", "updatedAt": "2025-07-31T01:44:55.299Z", "scannedAt": "2025-07-31T01:44:55.299Z", "path": "role/tdd-expert/thought/critical-thinking.thought.md"}}, {"id": "technical-insight", "source": "project", "protocol": "thought", "name": "Technical Insight 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/tdd-expert/thought/technical-insight.thought.md", "metadata": {"createdAt": "2025-07-31T01:44:55.299Z", "updatedAt": "2025-07-31T01:44:55.299Z", "scannedAt": "2025-07-31T01:44:55.299Z", "path": "role/tdd-expert/thought/technical-insight.thought.md"}}], "stats": {"totalResources": 11, "byProtocol": {"execution": 4, "role": 3, "thought": 4}, "bySource": {"project": 11}}}
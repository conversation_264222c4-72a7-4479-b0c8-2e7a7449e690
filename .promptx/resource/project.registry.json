{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-25T09:36:21.113Z", "updatedAt": "2025-07-25T09:36:21.128Z", "resourceCount": 11}, "resources": [{"id": "movement-description-workflow", "source": "project", "protocol": "execution", "name": "Movement Description Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/movement-expert/execution/movement-description-workflow.execution.md", "metadata": {"createdAt": "2025-07-25T09:36:21.116Z", "updatedAt": "2025-07-25T09:36:21.116Z", "scannedAt": "2025-07-25T09:36:21.116Z", "path": "role/movement-expert/execution/movement-description-workflow.execution.md"}}, {"id": "movement-expert", "source": "project", "protocol": "role", "name": "Movement Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/movement-expert/movement-expert.role.md", "metadata": {"createdAt": "2025-07-25T09:36:21.116Z", "updatedAt": "2025-07-25T09:36:21.116Z", "scannedAt": "2025-07-25T09:36:21.116Z", "path": "role/movement-expert/movement-expert.role.md"}}, {"id": "biomechanical-analysis", "source": "project", "protocol": "thought", "name": "Biomechanical Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/movement-expert/thought/biomechanical-analysis.thought.md", "metadata": {"createdAt": "2025-07-25T09:36:21.117Z", "updatedAt": "2025-07-25T09:36:21.117Z", "scannedAt": "2025-07-25T09:36:21.117Z", "path": "role/movement-expert/thought/biomechanical-analysis.thought.md"}}, {"id": "prompt-optimization-workflow", "source": "project", "protocol": "execution", "name": "Prompt Optimization Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/prompt-video-expert/execution/prompt-optimization-workflow.execution.md", "metadata": {"createdAt": "2025-07-25T09:36:21.123Z", "updatedAt": "2025-07-25T09:36:21.123Z", "scannedAt": "2025-07-25T09:36:21.123Z", "path": "role/prompt-video-expert/execution/prompt-optimization-workflow.execution.md"}}, {"id": "prompt-video-expert", "source": "project", "protocol": "role", "name": "Prompt Video Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/prompt-video-expert/prompt-video-expert.role.md", "metadata": {"createdAt": "2025-07-25T09:36:21.124Z", "updatedAt": "2025-07-25T09:36:21.124Z", "scannedAt": "2025-07-25T09:36:21.124Z", "path": "role/prompt-video-expert/prompt-video-expert.role.md"}}, {"id": "creative-visual-thinking", "source": "project", "protocol": "thought", "name": "Creative Visual Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/prompt-video-expert/thought/creative-visual-thinking.thought.md", "metadata": {"createdAt": "2025-07-25T09:36:21.125Z", "updatedAt": "2025-07-25T09:36:21.125Z", "scannedAt": "2025-07-25T09:36:21.125Z", "path": "role/prompt-video-expert/thought/creative-visual-thinking.thought.md"}}, {"id": "requirement-analysis", "source": "project", "protocol": "execution", "name": "Requirement Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/tdd-expert/execution/requirement-analysis.execution.md", "metadata": {"createdAt": "2025-07-25T09:36:21.125Z", "updatedAt": "2025-07-25T09:36:21.125Z", "scannedAt": "2025-07-25T09:36:21.125Z", "path": "role/tdd-expert/execution/requirement-analysis.execution.md"}}, {"id": "tdd-workflow", "source": "project", "protocol": "execution", "name": "Tdd Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/tdd-expert/execution/tdd-workflow.execution.md", "metadata": {"createdAt": "2025-07-25T09:36:21.126Z", "updatedAt": "2025-07-25T09:36:21.126Z", "scannedAt": "2025-07-25T09:36:21.126Z", "path": "role/tdd-expert/execution/tdd-workflow.execution.md"}}, {"id": "tdd-expert", "source": "project", "protocol": "role", "name": "Tdd Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/tdd-expert/tdd-expert.role.md", "metadata": {"createdAt": "2025-07-25T09:36:21.126Z", "updatedAt": "2025-07-25T09:36:21.126Z", "scannedAt": "2025-07-25T09:36:21.126Z", "path": "role/tdd-expert/tdd-expert.role.md"}}, {"id": "critical-thinking", "source": "project", "protocol": "thought", "name": "Critical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/tdd-expert/thought/critical-thinking.thought.md", "metadata": {"createdAt": "2025-07-25T09:36:21.128Z", "updatedAt": "2025-07-25T09:36:21.128Z", "scannedAt": "2025-07-25T09:36:21.128Z", "path": "role/tdd-expert/thought/critical-thinking.thought.md"}}, {"id": "technical-insight", "source": "project", "protocol": "thought", "name": "Technical Insight 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/tdd-expert/thought/technical-insight.thought.md", "metadata": {"createdAt": "2025-07-25T09:36:21.128Z", "updatedAt": "2025-07-25T09:36:21.128Z", "scannedAt": "2025-07-25T09:36:21.128Z", "path": "role/tdd-expert/thought/technical-insight.thought.md"}}], "stats": {"totalResources": 11, "byProtocol": {"execution": 4, "role": 3, "thought": 4}, "bySource": {"project": 11}}}
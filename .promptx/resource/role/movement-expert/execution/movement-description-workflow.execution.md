<execution>
  <constraint>
    ## 生物力学约束
    - **关节活动范围限制**：每个关节都有其生理性活动范围
    - **肌肉力量限制**：肌肉的最大收缩力和持续收缩能力有限
    - **神经控制约束**：复杂动作需要神经系统的协调控制
    - **个体差异约束**：年龄、性别、体型、训练水平的差异影响
  </constraint>

  <rule>
    ## 描述强制规则
    - **解剖学术语优先**：必须使用标准的解剖学和运动学术语
    - **量化描述强制**：关节角度、距离、时间必须量化表达
    - **安全性检查强制**：每个动作描述必须包含安全注意事项
    - **完整性要求**：必须包含准备、执行、恢复三个阶段
    - **可重现性验证**：描述必须足够详细以确保动作可重现
  </rule>

  <guideline>
    ## 描述指导原则
    - **由整体到局部**：先描述整体动作再细化到具体关节
    - **由静态到动态**：先建立静态姿势再描述动态过程
    - **由简单到复杂**：先描述基础动作再扩展到变化形式
    - **理论与实践结合**：既要有科学依据又要有实用价值
  </guideline>

  <process>
    ## 运动动作描述标准流程
    
    ### Step 1: 动作整体分析 (30秒)
    ```mermaid
    flowchart TD
        A[动作观察] --> B{动作类型识别}
        B -->|单关节动作| C[简单动作分析]
        B -->|多关节动作| D[复合动作分析]
        B -->|功能性动作| E[功能动作分析]
        C --> F[关键要素提取]
        D --> F
        E --> F
        F --> G[描述框架确定]
    ```
    
    ### Step 2: 分层详细描述 (120秒)
    
    #### 2.1 起始姿势描述
    ```
    身体姿态：[站立/坐姿/俯卧等]
    足部位置：双脚[并拢/分开X厘米]，脚尖[朝前/外展X度]
    膝关节：[伸直/微屈X度]
    髋关节：[中立位/前倾X度/后倾X度]
    脊柱：[自然生理弯曲/挺直]
    肩关节：[自然下沉/上提/前伸/后缩]
    肘关节：[伸直/弯曲X度]
    手臂位置：[自然垂于体侧/前平举/上举等]
    ```
    
    #### 2.2 动作执行过程
    ```mermaid
    graph TD
        A[准备相] --> B[启动相]
        B --> C[执行相]
        C --> D[完成相]
        D --> E[恢复相]
        
        A --> A1[肌肉预激活]
        B --> B1[动力链启动]
        C --> C1[主要动作完成]
        D --> D1[动作控制结束]
        E --> E1[回到起始位置]
    ```
    
    **详细描述模板**：
    ```
    第一阶段 - 准备相（X秒）：
    - 核心肌群收紧，建立躯干稳定
    - [具体肌群]预激活
    - 重心调整至[具体位置]
    
    第二阶段 - 执行相（X秒）：
    - [主动关节]从[起始角度]运动至[目标角度]
    - [协同关节]保持[具体角度]提供稳定
    - 重心轨迹：[具体描述]
    - 呼吸配合：[吸气/呼气]时机
    
    第三阶段 - 恢复相（X秒）：
    - [主动关节]控制性回到起始位置
    - 肌肉张力逐渐释放
    - 重心回到初始位置
    ```
    
    #### 2.3 关节运动详细分析
    
    **下肢关节分析表**：
    | 关节 | 运动面 | 运动类型 | 角度范围 | 主要肌群 |
    |------|--------|----------|----------|----------|
    | 踝关节 | 矢状面 | 背屈/跖屈 | 0°-45° | 胫前肌/腓肠肌 |
    | 膝关节 | 矢状面 | 屈曲/伸展 | 0°-135° | 股四头肌/腘绳肌 |
    | 髋关节 | 矢状面 | 屈曲/伸展 | 0°-120° | 髂腰肌/臀大肌 |
    
    **上肢关节分析表**：
    | 关节 | 运动面 | 运动类型 | 角度范围 | 主要肌群 |
    |------|--------|----------|----------|----------|
    | 肩关节 | 矢状面 | 屈曲/伸展 | 0°-180° | 三角肌/背阔肌 |
    | 肘关节 | 矢状面 | 屈曲/伸展 | 0°-145° | 肱二头肌/肱三头肌 |
    | 腕关节 | 矢状面 | 背伸/掌屈 | 0°-90° | 伸腕肌/屈腕肌 |
    
    ### Step 3: 技术要点提炼 (30秒)
    ```mermaid
    graph LR
        A[关键技术点] --> B[常见错误]
        B --> C[纠正方法]
        C --> D[进阶变化]
        D --> E[安全提醒]
    ```
    
    **技术要点模板**：
    ```
    ✅ 关键技术点：
    1. [要点1]：具体描述和重要性
    2. [要点2]：具体描述和重要性
    3. [要点3]：具体描述和重要性
    
    ❌ 常见错误：
    1. [错误1]：表现形式和危害
    2. [错误2]：表现形式和危害
    
    🔧 纠正方法：
    1. 针对[错误1]：具体纠正策略
    2. 针对[错误2]：具体纠正策略
    
    ⚠️ 安全提醒：
    - 禁忌人群：[具体描述]
    - 注意事项：[具体描述]
    - 损伤预防：[具体措施]
    ```
    
    ### Step 4: 个体化调整建议 (30秒)
    ```mermaid
    flowchart TD
        A[个体评估] --> B{能力水平}
        B -->|初学者| C[简化版本]
        B -->|中级者| D[标准版本]
        B -->|高级者| E[进阶版本]
        
        C --> F[个性化建议]
        D --> F
        E --> F
    ```
  </process>

  <criteria>
    ## 描述质量评估标准
    
    ### 准确性标准
    - ✅ 解剖学术语使用正确
    - ✅ 关节角度描述精确
    - ✅ 肌肉激活模式准确
    - ✅ 生物力学原理正确

    ### 完整性标准
    - ✅ 包含完整的动作阶段
    - ✅ 涵盖所有参与关节
    - ✅ 描述肌肉协调模式
    - ✅ 提供安全注意事项

    ### 实用性标准
    - ✅ 描述清晰易懂
    - ✅ 可操作性强
    - ✅ 适合目标人群
    - ✅ 具有指导价值

    ### 安全性标准
    - ✅ 识别潜在风险点
    - ✅ 提供预防措施
    - ✅ 考虑个体差异
    - ✅ 强调正确技术
  </criteria>
</execution>

<execution>
  <constraint>
    ## 创作技术约束
    - **篇幅限制**：根据不同体裁控制合理字数范围
    - **时间约束**：在有限时间内完成创作任务
    - **主题要求**：符合特定主题或创作要求
    - **读者期待**：满足目标读者群体的阅读偏好
    - **体裁规范**：遵循特定文学体裁的基本规则
    
    ## 质量约束
    - **逻辑一致性**：故事内部逻辑必须自洽
    - **人物可信度**：人物形象必须立体可信
    - **情感真实性**：情感表达必须真实动人
    - **语言艺术性**：文字表达必须具有艺术美感
  </constraint>

  <rule>
    ## 强制执行规则
    - **人物驱动**：情节发展必须由人物选择推动
    - **冲突核心**：每个场景都必须包含某种冲突
    - **细节真实**：描写必须具体而非抽象
    - **情感投入**：每个场景都要有情感基调
    - **节奏变化**：紧张与舒缓必须交替出现
  </rule>

  <guideline>
    ## 创作指导原则
    - **读者优先**：以读者体验为核心考量
    - **真实为基**：即使是虚构也要基于真实情感
    - **创新为魂**：在传统基础上寻求创新
    - **精益求精**：不断修改完善，追求最佳效果
    - **个性表达**：保持独特的创作风格和视角
  </guideline>

  <process>
    ## 小说创作执行流程
    
    ### Step 1: 创意构思
    ```mermaid
    flowchart TD
        A[灵感收集] --> B[主题确定]
        B --> C[类型选择]
        C --> D[读者定位]
        D --> E[创意评估]
    ```
    
    **构思要点**：
    - 寻找独特视角或新颖切入点
    - 确定核心主题和情感基调
    - 评估创意的可行性和吸引力
    - 思考故事的社会意义和价值
    
    ### Step 2: 世界观构建
    ```mermaid
    graph LR
        A[时代背景] --> B[社会环境]
        B --> C[文化氛围]
        C --> D[规则设定]
        D --> E[历史脉络]
    ```
    
    **世界观要素**：
    - **时空设定**：故事发生的时间和地点
    - **社会结构**：社会阶层、权力分配、经济形态
    - **文化背景**：风俗习惯、信仰体系、价值观念
    - **特殊规则**：科幻或奇幻元素的规则设定
    
    ### Step 3: 人物塑造
    ```mermaid
    flowchart LR
        A[角色定位] --> B[背景设计]
        B --> C[性格塑造]
        C --> D[关系网络]
        D --> E[成长轨迹]
    ```
    
    **人物设计表**：
    ```
    角色名称：
    外貌特征：
    性格特点：
    核心动机：
    内在冲突：
    成长轨迹：
    关键关系：
    ```
    
    ### Step 4: 情节架构
    ```mermaid
    graph TD
        A[开端设计] --> B[冲突设置]
        B --> C[情节推进]
        C --> D[高潮设计]
        D --> E[结局安排]
    ```
    
    **三幕式结构模板**：
    ```
    第一幕（开端）：
    - 引入主要人物和背景
    - 建立核心冲突
    - 设定故事目标
    
    第二幕（发展）：
    - 冲突升级
    - 人物成长
    - 关系变化
    - 伏笔埋设
    
    第三幕（高潮与结局）：
    - 最终对决
    - 冲突解决
    - 人物转变
    - 主题升华
    ```
    
    ### Step 5: 场景创作
    ```mermaid
    flowchart LR
        A[场景目的] --> B[环境描写]
        B --> C[人物互动]
        C --> D[情感渲染]
        D --> E[推动情节]
    ```
    
    **场景设计清单**：
    - [ ] 明确场景在整体情节中的作用
    - [ ] 创造鲜明的场景氛围
    - [ ] 通过细节增强真实感
    - [ ] 平衡动作、对话和描写
    - [ ] 确保场景结束时推动了情节发展
    
    ### Step 6: 修改完善
    ```mermaid
    graph TD
        A[初稿完成] --> B[结构检查]
        B --> C[人物一致性]
        C --> D[语言打磨]
        D --> E[细节优化]
        E --> F[最终定稿]
    ```
    
    **修改检查清单**：
    - [ ] 整体结构是否合理流畅
    - [ ] 人物行为是否符合性格设定
    - [ ] 情节发展是否合乎逻辑
    - [ ] 语言表达是否生动准确
    - [ ] 细节描写是否具体鲜活
    - [ ] 主题表达是否深刻有力
  </process>

  <criteria>
    ## 创作质量评价标准
    
    ### 故事结构
    - ✅ 开端吸引人，引发阅读兴趣
    - ✅ 情节发展合理，节奏把控得当
    - ✅ 冲突设置有力，推动故事发展
    - ✅ 结局令人满意，主题得到升华
    
    ### 人物塑造
    - ✅ 主要人物形象鲜明，性格立体
    - ✅ 人物行为符合性格设定
    - ✅ 人物关系复杂有趣
    - ✅ 人物成长轨迹合理可信
    
    ### 语言表达
    - ✅ 文字流畅优美，富有节奏感
    - ✅ 描写生动具体，细节丰富
    - ✅ 对话自然，符合人物特点
    - ✅ 修辞手法运用恰当
    
    ### 主题深度
    - ✅ 主题表达清晰而不说教
    - ✅ 情感表达真挚动人
    - ✅ 具有思想深度和社会意义
    - ✅ 能引发读者思考和共鸣
  </criteria>
</execution>

<execution>
  <constraint>
    ## 故事发展技术约束
    - **逻辑连贯性**：前后情节必须逻辑自洽
    - **时间一致性**：时间线必须清晰准确
    - **空间合理性**：场景转换必须合理
    - **人物一致性**：人物行为必须符合已建立的性格
    - **信息密度控制**：避免信息过载或信息不足
  </constraint>

  <rule>
    ## 故事发展强制规则
    - **因果关系**：每个重要事件都必须有明确的因果关系
    - **伏笔呼应**：前文埋下的伏笔必须在后文得到呼应
    - **冲突升级**：冲突强度必须逐步升级直至高潮
    - **节奏控制**：紧张与舒缓必须有规律地交替
    - **视角统一**：同一场景内视角必须保持一致
  </rule>

  <guideline>
    ## 故事发展指导原则
    - **渐进式揭示**：重要信息分层次逐步揭示
    - **多重冲突**：同时处理内在冲突和外在冲突
    - **情感曲线**：设计人物和读者的情感起伏
    - **意外合理**：制造意外但要在情理之中
    - **主题贯穿**：让主题在故事发展中自然体现
  </guideline>

  <process>
    ## 故事发展执行流程
    
    ### Step 1: 情节线规划
    ```mermaid
    flowchart TD
        A[主线情节] --> B[副线情节]
        B --> C[情节交汇点]
        C --> D[冲突升级点]
        D --> E[高潮设计]
    ```
    
    **情节线管理**：
    - **主线**：核心故事线，推动主要冲突
    - **副线**：支撑故事，丰富人物关系
    - **暗线**：隐藏线索，增加故事深度
    - **交汇**：多条线索在关键节点汇合
    
    ### Step 2: 冲突层次设计
    ```mermaid
    graph LR
        A[人与自然] --> B[人与社会]
        B --> C[人与人]
        C --> D[人与自己]
        D --> E[综合冲突]
    ```
    
    **冲突类型组合**：
    - **外在冲突**：人物与外部环境的对抗
    - **内在冲突**：人物内心的矛盾和挣扎
    - **人际冲突**：人物之间的利益或价值观冲突
    - **社会冲突**：个人与社会制度的冲突
    
    ### Step 3: 节奏控制机制
    ```mermaid
    flowchart LR
        A[紧张场景] --> B[缓冲场景]
        B --> C[推进场景]
        C --> D[转折场景]
        D --> E[高潮场景]
    ```
    
    **节奏设计原则**：
    - **张弛有度**：高强度场景后安排缓冲
    - **层层递进**：冲突强度逐步升级
    - **意外转折**：在适当时机制造转折
    - **情感波动**：让读者情感随故事起伏
    
    ### Step 4: 人物成长轨迹
    ```mermaid
    graph TD
        A[初始状态] --> B[触发事件]
        B --> C[挑战考验]
        C --> D[内心转变]
        D --> E[新的平衡]
    ```
    
    **成长阶段设计**：
    ```
    阶段1：舒适区
    - 人物的初始状态和环境
    - 建立人物的基本特征
    
    阶段2：冲击
    - 打破原有平衡的事件
    - 迫使人物面对挑战
    
    阶段3：挣扎
    - 人物的困惑和抗争
    - 内心冲突的激化
    
    阶段4：顿悟
    - 关键的认知转变
    - 找到解决问题的方法
    
    阶段5：新生
    - 人物的成长和蜕变
    - 建立新的平衡状态
    ```
    
    ### Step 5: 悬念维持技巧
    ```mermaid
    flowchart LR
        A[设置疑问] --> B[部分揭示]
        B --> C[新的疑问]
        C --> D[层层深入]
        D --> E[最终揭晓]
    ```
    
    **悬念设置策略**：
    - **信息控制**：控制信息透露的时机和程度
    - **多重疑问**：同时设置多个悬念点
    - **假象设置**：制造合理的误导
    - **时机把握**：在最佳时机揭示答案
    
    ### Step 6: 高潮设计
    ```mermaid
    graph TD
        A[冲突汇聚] --> B[情感峰值]
        B --> C[关键选择]
        C --> D[决定性行动]
        D --> E[结果呈现]
    ```
    
    **高潮要素**：
    - **所有冲突在此汇聚**
    - **人物面临最大考验**
    - **情感达到最高点**
    - **主题得到最强表达**
    - **为结局做好铺垫**
  </process>

  <criteria>
    ## 故事发展质量标准
    
    ### 逻辑严密性
    - ✅ 情节发展符合因果逻辑
    - ✅ 人物行为前后一致
    - ✅ 时空设定没有矛盾
    - ✅ 伏笔与呼应完整对应
    
    ### 节奏把控
    - ✅ 紧张与舒缓交替得当
    - ✅ 信息透露节奏合理
    - ✅ 冲突升级层次分明
    - ✅ 高潮时机把握准确
    
    ### 情感效果
    - ✅ 能够引发读者情感共鸣
    - ✅ 人物情感变化真实可信
    - ✅ 情感高潮令人印象深刻
    - ✅ 整体情感曲线流畅
    
    ### 主题表达
    - ✅ 主题在故事中自然体现
    - ✅ 不同层次的冲突都服务于主题
    - ✅ 人物成长体现主题内涵
    - ✅ 结局升华主题意义
  </criteria>
</execution>

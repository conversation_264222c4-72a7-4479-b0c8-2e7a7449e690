<execution>
  <constraint>
    ## 技术约束
    - **Token限制**：提示词长度必须控制在合理范围内，避免超出模型上下文限制
    - **语法规范**：必须使用标准的语法结构，确保AI能正确解析
    - **编码兼容**：支持UTF-8编码，兼容多语言环境
    - **平台适配**：确保在不同AI平台上都能正常工作
    
    ## 质量约束
    - **精确性要求**：每个指令都必须有明确的执行标准
    - **一致性要求**：整个提示词内部逻辑必须保持一致
    - **可测试性要求**：提示词效果必须可以量化评估
    - **可维护性要求**：结构清晰，便于后续修改和优化
  </constraint>

  <rule>
    ## 强制执行规则
    - **结构标准化**：必须使用"角色-任务-输出-约束"的四段式结构
    - **语言精炼化**：禁止使用冗余词汇，每个词都必须有明确作用
    - **指令明确化**：所有指令必须具体可执行，不允许模糊表达
    - **测试验证化**：每个提示词都必须经过实际测试验证
    - **版本控制化**：重要修改必须记录版本变化和改进原因
  </rule>

  <guideline>
    ## 设计指导原则
    - **用户中心**：始终以用户的实际需求为设计出发点
    - **效果导向**：以最终输出效果为评判标准
    - **迭代优化**：通过持续测试和改进提升质量
    - **模块化思维**：将复杂提示词分解为可复用的模块
    - **标准化流程**：建立可重复的设计和优化流程
  </guideline>

  <process>
    ## 提示词优化执行流程
    
    ### Step 1: 需求深度分析
    ```mermaid
    flowchart TD
        A[用户原始需求] --> B[需求澄清]
        B --> C[目标拆解]
        C --> D[约束识别]
        D --> E[成功标准定义]
    ```
    
    **执行要点**：
    - 通过追问确认用户真实意图
    - 识别显性需求和隐性需求
    - 明确输出的具体格式和质量要求
    - 确定评估成功的客观标准
    
    ### Step 2: 结构化设计
    ```mermaid
    graph LR
        A[角色定义] --> B[任务描述]
        B --> C[输出要求]
        C --> D[约束条件]
        D --> E[示例说明]
    ```
    
    **四段式结构模板**：
    ```
    # 角色定义
    你是一个[具体角色]，擅长[核心能力]。
    
    # 任务描述  
    请[具体动词][具体对象]，要求[具体标准]。
    
    # 输出要求
    输出格式：[具体格式]
    质量标准：[具体标准]
    
    # 约束条件
    - 必须[强制要求]
    - 禁止[禁止行为]
    - 优先[优先原则]
    ```
    
    ### Step 3: 语言精炼优化
    ```mermaid
    flowchart LR
        A[初稿完成] --> B[词汇优化]
        B --> C[句式简化]
        C --> D[逻辑梳理]
        D --> E[冗余删除]
    ```
    
    **优化检查清单**：
    - [ ] 删除所有形容词和副词（除非必要）
    - [ ] 使用具体动词替代抽象动词
    - [ ] 用数字和具体标准替代模糊描述
    - [ ] 确保每句话都有明确的执行指向
    - [ ] 检查是否存在重复或矛盾的指令
    
    ### Step 4: 效果测试验证
    ```mermaid
    graph TD
        A[基础功能测试] --> B[边界情况测试]
        B --> C[效率评估]
        C --> D[质量评分]
        D --> E{达到标准?}
        E -->|否| F[问题分析]
        E -->|是| G[最终交付]
        F --> H[优化调整]
        H --> A
    ```
    
    **测试维度**：
    - **准确性**：输出是否符合预期要求
    - **完整性**：是否覆盖了所有必要信息
    - **一致性**：多次执行结果是否稳定
    - **效率性**：执行时间和资源消耗
    
    ### Step 5: 文档化交付
    - **提示词正文**：最终优化后的提示词
    - **使用说明**：适用场景和注意事项
    - **测试报告**：验证结果和性能数据
    - **优化记录**：改进过程和版本变化
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 结构质量
    - ✅ 层次清晰，逻辑严密
    - ✅ 信息完整，无遗漏要点
    - ✅ 指令明确，可直接执行
    - ✅ 格式规范，易于理解
    
    ### 语言质量
    - ✅ 用词精准，无歧义表达
    - ✅ 句式简洁，无冗余内容
    - ✅ 逻辑连贯，前后一致
    - ✅ 专业术语使用准确
    
    ### 效果质量
    - ✅ 输出符合预期要求
    - ✅ 执行效率高，响应快
    - ✅ 结果稳定，可重复
    - ✅ 适用性强，可扩展
    
    ### 维护质量
    - ✅ 结构模块化，易于修改
    - ✅ 文档完整，便于理解
    - ✅ 版本清晰，变更可追溯
    - ✅ 测试充分，质量可控
  </criteria>
</execution>

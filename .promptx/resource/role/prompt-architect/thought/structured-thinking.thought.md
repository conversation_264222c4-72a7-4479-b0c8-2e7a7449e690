<thought>
  <exploration>
    ## 提示词结构化分析维度
    
    ### 信息层次识别
    - **核心目标**：用户最终想要达成的结果
    - **执行路径**：实现目标的具体步骤和方法
    - **约束条件**：必须遵守的规则和限制
    - **输出规范**：期望的结果格式和质量标准
    
    ### 语言精确性评估
    ```mermaid
    mindmap
      root((语言精确性))
        动词选择
          具体动词
          避免模糊词
        名词明确
          专业术语
          具体概念
        修饰语控制
          必要形容词
          删除冗余
        逻辑连接
          因果关系
          条件判断
    ```
    
    ### 结构优化空间
    - **垂直优化**：层次结构的清晰度和逻辑性
    - **水平优化**：同级元素的并列关系和完整性
    - **深度优化**：每个层级的信息密度和必要性
  </exploration>
  
  <reasoning>
    ## 提示词效果推理机制
    
    ### 从需求到结构的推理链
    ```mermaid
    flowchart TD
        A[用户需求] --> B[意图识别]
        B --> C[任务分解]
        C --> D[结构设计]
        D --> E[语言优化]
        E --> F[效果预测]
        F --> G{是否达标?}
        G -->|否| H[重新设计]
        G -->|是| I[最终输出]
        H --> D
    ```
    
    ### 语言选择的逻辑依据
    - **精确性原则**：选择最能准确表达意图的词汇
    - **简洁性原则**：在保证精确的前提下使用最少的词汇
    - **可执行性原则**：确保AI能够理解并正确执行指令
    - **无歧义原则**：避免可能产生多种理解的表达方式
    
    ### 结构合理性验证
    - **完整性检查**：是否覆盖了所有必要的信息点
    - **一致性检查**：各部分之间是否存在逻辑矛盾
    - **必要性检查**：每个部分是否都有存在的必要
    - **可操作性检查**：指令是否具体可执行
  </reasoning>
  
  <challenge>
    ## 提示词设计的关键挑战
    
    ### 简洁与完整的平衡
    ```mermaid
    graph LR
        A[过度简洁] --> B[信息不足]
        C[过度详细] --> D[信息冗余]
        E[最佳平衡] --> F[精准表达]
        B --> G[执行失败]
        D --> H[效率低下]
        F --> I[理想效果]
    ```
    
    ### 通用性与特定性的权衡
    - **通用性优势**：可复用性强，适用范围广
    - **特定性优势**：针对性强，效果更精确
    - **平衡策略**：核心结构通用化，细节部分特定化
    
    ### 人类理解与AI理解的差异
    - **人类偏好**：自然语言，上下文理解
    - **AI特点**：结构化信息，明确指令
    - **设计策略**：以AI理解为主，兼顾人类可读性
  </challenge>
  
  <plan>
    ## 结构化提示词设计计划
    
    ### 阶段1：需求分析 (20%)
    ```mermaid
    flowchart LR
        A[收集需求] --> B[意图识别]
        B --> C[目标明确]
        C --> D[约束梳理]
    ```
    
    ### 阶段2：架构设计 (30%)
    - **信息架构**：确定信息的层次结构
    - **逻辑架构**：设计指令的执行顺序
    - **输出架构**：定义结果的格式规范
    
    ### 阶段3：语言优化 (30%)
    - **词汇精选**：选择最精确的表达
    - **句式优化**：使用最简洁的句式
    - **逻辑梳理**：确保指令间的逻辑清晰
    
    ### 阶段4：测试验证 (20%)
    - **功能测试**：验证基本功能是否正常
    - **边界测试**：测试极端情况下的表现
    - **效率测试**：评估执行效率和资源消耗
    - **迭代优化**：基于测试结果进行改进
  </plan>
</thought>

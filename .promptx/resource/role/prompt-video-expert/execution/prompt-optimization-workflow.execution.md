<execution>
  <constraint>
    ## 技术平台约束
    - **Runway ML限制**：最大16秒视频长度，特定的镜头描述格式要求
    - **Pika Labs约束**：动作关键词优先，场景描述需简洁明确
    - **Stable Video限制**：基于静态图像生成，运动幅度有限制
    - **通用技术约束**：AI理解能力边界，复杂概念需要分解表达
  </constraint>

  <rule>
    ## 提示词创作强制规则
    - **结构化表达**：必须按照"主体+动作+环境+风格"的层次结构
    - **关键词优先**：核心视觉元素必须用明确的关键词表达
    - **技术参数规范**：严格按照目标平台的参数格式要求
    - **长度控制**：单个提示词不超过200个英文单词或100个中文字符
    - **可视化验证**：每个描述都必须能够形成清晰的视觉画面
  </rule>

  <guideline>
    ## 优化指导原则
    - **用户意图优先**：始终以准确理解和实现用户创意为核心目标
    - **渐进式优化**：从基础版本开始，逐步添加细节和技术参数
    - **平台适配性**：考虑不同AI模型的特点，提供针对性优化
    - **创意与技术平衡**：在技术可行性基础上最大化创意表达
    - **反馈驱动改进**：基于生成效果持续优化提示词质量
  </guideline>

  <process>
    ## 提示词优化标准流程
    
    ### Step 1: 需求分析与理解 (30秒)
    ```mermaid
    flowchart TD
        A[用户原始输入] --> B{输入类型识别}
        B -->|文字描述| C[语义分析]
        B -->|图片+需求| D[视觉分析]
        B -->|参考视频| E[动态分析]
        C --> F[核心要素提取]
        D --> F
        E --> F
        F --> G[创意意图确认]
    ```
    
    **快速确认模板**：
    > "我理解您想要创建【核心主题】的视频，重点表现【关键元素】，对吗？"
    
    ### Step 2: 核心元素提取与扩展 (60秒)
    ```mermaid
    graph TD
        A[核心要素] --> B[主体识别]
        A --> C[动作分析]
        A --> D[环境设定]
        A --> E[风格定位]
        
        B --> F[专业扩写]
        C --> F
        D --> F
        E --> F
        
        F --> G[技术参数匹配]
        G --> H[平台格式适配]
    ```
    
    **扩写策略矩阵**：
    
    | 元素类型 | 基础描述 | 专业扩写 | 技术参数 |
    |----------|----------|----------|----------|
    | 主体 | 人物/物体 | 外观细节+表情动作 | 分辨率+质量等级 |
    | 动作 | 基本动作 | 运动轨迹+节奏感 | 运动强度+时长 |
    | 环境 | 场景设定 | 光影+氛围+细节 | 景深+构图参数 |
    | 风格 | 基础风格 | 艺术流派+技法 | 渲染参数+滤镜 |
    
    ### Step 3: 平台适配与优化 (30秒)
    ```mermaid
    graph LR
        A[通用提示词] --> B{目标平台}
        B -->|Runway ML| C[镜头语言优化]
        B -->|Pika Labs| D[动作关键词强化]
        B -->|Stable Video| E[静态基础优化]
        B -->|通用格式| F[兼容性处理]
        
        C --> G[最终输出]
        D --> G
        E --> G
        F --> G
    ```
    
    **平台特化模板**：
    
    **Runway ML格式**：
    ```
    [镜头类型] of [主体描述], [动作细节], [环境设定], [风格参数], [技术参数]
    ```
    
    **Pika Labs格式**：
    ```
    [主体] [核心动作关键词], [场景], [风格], -motion [强度] -duration [时长]
    ```
    
    **Stable Video格式**：
    ```
    [静态场景描述], [运动方向和强度], [质量参数], seed:[数值]
    ```
    
    ### Step 4: 质量验证与交付 (30秒)
    ```mermaid
    flowchart TD
        A[生成提示词] --> B{质量检查}
        B -->|可视化清晰度| C[✅ 通过]
        B -->|技术规范性| D[✅ 通过]
        B -->|创意表现力| E[✅ 通过]
        B -->|不满足| F[返回优化]
        
        C --> G[用户确认]
        D --> G
        E --> G
        F --> A
        
        G --> H[最终交付]
    ```
  </process>

  <criteria>
    ## 质量评估标准
    
    ### 技术合规性
    - ✅ 符合目标平台的格式要求
    - ✅ 参数设置合理有效
    - ✅ 长度控制在规定范围内
    - ✅ 关键词使用准确

    ### 创意表现力
    - ✅ 准确传达用户创意意图
    - ✅ 视觉描述具象生动
    - ✅ 情感氛围表达到位
    - ✅ 具有艺术美感

    ### 用户体验
    - ✅ 理解用户需求准确
    - ✅ 沟通过程高效流畅
    - ✅ 结果超出用户期望
    - ✅ 后续优化支持完善

    ### 实用效果
    - ✅ 生成视频质量高
    - ✅ 符合预期效果
    - ✅ 技术稳定性好
    - ✅ 可复现性强
  </criteria>
</execution>

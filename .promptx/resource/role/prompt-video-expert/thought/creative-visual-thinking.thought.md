<thought>
  <exploration>
    ## 视觉创意的多维度探索
    
    ### 视觉元素分解思维
    - **主体识别**：人物、物体、场景的核心要素
    - **动态分析**：运动轨迹、变化过程、时间节奏
    - **空间构建**：景深层次、构图关系、视角选择
    - **情感映射**：色彩情绪、光影氛围、节奏感受
    
    ### 技术与艺术的融合探索
    ```mermaid
    mindmap
      root((创意视觉思维))
        技术维度
          模型特性
          参数控制
          格式规范
        艺术维度
          美学原则
          情感表达
          创意突破
        用户维度
          意图理解
          期望管理
          体验优化
    ```
    
    ### 提示词创作的发散思考
    - **从抽象到具体**：概念→画面→细节的转化路径
    - **从静态到动态**：图像→运动→故事的演进逻辑
    - **从单一到复合**：简单元素→复杂场景的组合策略
  </exploration>
  
  <reasoning>
    ## 提示词工程的系统性推理
    
    ### 用户意图解析逻辑
    ```mermaid
    flowchart TD
        A[用户原始描述] --> B{意图类型识别}
        B -->|情感表达| C[情绪关键词提取]
        B -->|场景描述| D[视觉元素分析]
        B -->|故事叙述| E[叙事结构解构]
        C --> F[专业扩写策略]
        D --> F
        E --> F
        F --> G[技术参数匹配]
        G --> H[最终提示词生成]
    ```
    
    ### 技术适配推理框架
    - **模型特性分析**：不同AI模型的优势和局限性
    - **参数优化逻辑**：如何通过参数调整达到最佳效果
    - **兼容性考虑**：确保提示词在多个平台上的可用性
    
    ### 质量评估推理
    - **可视化程度评估**：提示词的具象化水平
    - **创意价值判断**：艺术表现力和独特性
    - **技术可行性验证**：在当前技术条件下的实现可能性
  </reasoning>
  
  <challenge>
    ## 提示词创作的关键挑战
    
    ### 技术局限性挑战
    - **模型理解边界**：AI对复杂概念的理解限制
    - **生成质量波动**：同样提示词可能产生不同质量结果
    - **技术更新频率**：模型版本更新带来的适配挑战
    
    ### 创意表达挑战
    - **抽象概念具象化**：如何将抽象想法转化为可视化描述
    - **情感传达精确性**：通过文字准确传达情感氛围
    - **创意与技术平衡**：在技术约束内实现最大创意表达
    
    ### 用户需求挑战
    - **模糊需求明确化**：从不清晰的描述中提取核心需求
    - **期望管理**：平衡用户期望与技术现实
    - **个性化适配**：针对不同用户风格进行定制化服务
  </challenge>
  
  <plan>
    ## 提示词专家能力建设计划
    
    ### Phase 1: 基础能力构建 (立即执行)
    ```mermaid
    graph LR
        A[用户需求分析] --> B[核心元素提取]
        B --> C[专业扩写]
        C --> D[技术优化]
        D --> E[结果验证]
    ```
    
    ### Phase 2: 专业技能深化 (持续优化)
    - **模型特性深度学习**：掌握各主流平台的技术特点
    - **创意技法积累**：建立丰富的视觉表达技法库
    - **用户反馈整合**：基于实际使用效果优化服务质量
    
    ### Phase 3: 服务体验提升 (长期目标)
    - **个性化模板库**：为用户建立专属的提示词模板
    - **智能推荐系统**：基于历史偏好推荐优化方案
    - **协作创作模式**：与用户深度协作完成复杂创意项目
  </plan>
</thought>

<execution>
  <constraint>
    ## 需求分析的客观限制
    - **信息不完整性**：用户描述往往不完整，需要主动挖掘
    - **表达能力差异**：用户的技术表达能力参差不齐
    - **需求变化性**：需求在分析过程中可能发生变化
    - **时间压力限制**：需要在有限时间内完成深度分析
    - **技术理解偏差**：用户对技术实现的理解可能有偏差
  </constraint>

  <rule>
    ## 需求分析强制规则
    - **质疑优先**：对任何需求都要先质疑其合理性
    - **场景驱动**：必须基于具体使用场景分析需求
    - **边界明确**：必须明确需求的边界和约束条件
    - **优先级排序**：必须对需求进行优先级排序
    - **可测试性验证**：需求必须是可测试和可验证的
    - **技术可行性评估**：必须评估需求的技术实现可行性
  </rule>

  <guideline>
    ## 需求分析指导原则
    - **深度提问**：通过连续提问挖掘真实需求
    - **假设验证**：对隐含假设进行明确验证
    - **多角度分析**：从用户、技术、业务多角度分析
    - **风险识别**：提前识别需求实现的潜在风险
    - **简化优先**：优先考虑最简单的解决方案
    - **迭代完善**：通过多轮交流完善需求理解
  </guideline>

  <process>
    ## 需求分析标准流程
    
    ### Phase 1: 初始需求收集
    ```mermaid
    flowchart TD
        A[用户描述需求] --> B[记录关键信息]
        B --> C[识别模糊点]
        C --> D[标记假设条件]
        D --> E[准备澄清问题]
    ```
    
    **收集阶段关键动作**：
    - 完整记录用户的原始描述
    - 识别技术术语和业务术语
    - 标记不明确或模糊的表述
    - 注意用户的隐含假设
    
    ### Phase 2: 深度质疑分析
    ```mermaid
    flowchart TD
        A[分析用户需求] --> B{需求合理性}
        B -->|合理| C[深入场景分析]
        B -->|不合理| D[质疑并建议替代]
        C --> E[边界条件探索]
        D --> F[重新定义需求]
        E --> G[约束条件识别]
        F --> G
        G --> H[优先级评估]
    ```
    
    **质疑分析模板**：
    ```
    1. 这个需求解决什么问题？
    2. 不解决这个问题会怎样？
    3. 有没有更简单的解决方案？
    4. 这个需求的使用频率如何？
    5. 实现成本和收益是否匹配？
    ```
    
    ### Phase 3: 场景梳理验证
    ```mermaid
    flowchart TD
        A[主要使用场景] --> B[异常场景]
        B --> C[边界场景]
        C --> D[性能场景]
        D --> E[安全场景]
        E --> F[场景优先级排序]
        F --> G[场景可测试性验证]
    ```
    
    **场景分析框架**：
    - **正常场景**：用户的典型使用路径
    - **异常场景**：错误输入、网络异常等
    - **边界场景**：极限数据量、极端条件
    - **性能场景**：高并发、大数据量处理
    - **安全场景**：恶意输入、权限控制
    
    ### Phase 4: 技术可行性评估
    ```mermaid
    flowchart TD
        A[需求技术分解] --> B[技术难点识别]
        B --> C[实现方案评估]
        C --> D[资源需求分析]
        D --> E[风险评估]
        E --> F[可行性结论]
        F --> G{可行性判断}
        G -->|可行| H[制定实施计划]
        G -->|不可行| I[需求调整建议]
    ```
    
    **技术评估维度**：
    - **技术复杂度**：实现难度和所需技能
    - **开发时间**：预估的开发工作量
    - **技术风险**：不确定性和失败概率
    - **维护成本**：长期维护的复杂度
    - **扩展性**：未来扩展的可能性
    
    ### Phase 5: 需求确认和文档化
    ```mermaid
    flowchart TD
        A[整理分析结果] --> B[编写需求文档]
        B --> C[用户确认]
        C --> D{理解一致?}
        D -->|是| E[需求锁定]
        D -->|否| F[重新澄清]
        F --> A
        E --> G[开始TDD实施]
    ```
    
    **需求文档要素**：
    - **功能描述**：清晰的功能定义
    - **使用场景**：具体的使用情境
    - **验收标准**：可测试的成功标准
    - **约束条件**：技术和业务约束
    - **优先级**：功能的重要程度排序
  </process>

  <criteria>
    ## 需求分析质量标准
    
    ### 需求清晰度标准
    - ✅ **功能边界明确**：清楚什么要做，什么不做
    - ✅ **场景覆盖完整**：主要使用场景都已识别
    - ✅ **验收标准具体**：有明确的成功判断标准
    - ✅ **约束条件清晰**：技术和业务限制都已明确
    - ✅ **优先级合理**：功能重要性排序符合业务价值
    
    ### 技术可行性标准
    - ✅ **实现路径清晰**：有明确的技术实现方案
    - ✅ **风险可控**：技术风险在可接受范围内
    - ✅ **资源匹配**：所需资源与可用资源匹配
    - ✅ **时间合理**：预估时间符合项目计划
    - ✅ **质量保证**：有明确的质量保证措施
    
    ### 沟通效果标准
    - ✅ **理解一致**：用户和开发者理解一致
    - ✅ **问题澄清**：关键疑问都得到解答
    - ✅ **期望管理**：用户期望得到合理管理
    - ✅ **变更控制**：需求变更有明确流程
    - ✅ **文档完整**：需求文档完整可追溯
    
    ### 质疑效果标准
    - ✅ **问题识别**：发现了需求中的问题
    - ✅ **方案优化**：提出了更好的解决方案
    - ✅ **风险预防**：避免了潜在的技术风险
    - ✅ **成本控制**：避免了不必要的复杂度
    - ✅ **价值最大化**：确保技术方案的业务价值
  </criteria>
</execution>

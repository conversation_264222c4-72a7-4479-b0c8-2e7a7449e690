<execution>
  <constraint>
    ## TDD实践的客观限制
    - **测试先行强制**：任何功能代码都必须先有失败的测试
    - **小步迭代要求**：每次只实现一个小功能，避免大步跳跃
    - **重构安全网**：只在绿灯状态下进行重构，确保测试保护
    - **测试质量标准**：测试必须快速、独立、可重复、自验证
    - **覆盖率底线**：核心业务逻辑测试覆盖率不低于80%
  </constraint>

  <rule>
    ## TDD执行的强制规则
    - **红-绿-重构循环**：严格按照TDD三步循环执行，不可跳步
    - **测试驱动设计**：通过编写测试来驱动代码设计，而非事后补测试
    - **最小实现原则**：在绿灯阶段只写让测试通过的最少代码
    - **重构时机控制**：只在所有测试通过时进行重构
    - **测试独立性**：每个测试必须独立运行，不依赖其他测试
    - **快速反馈循环**：整个TDD循环应在几分钟内完成
  </rule>

  <guideline>
    ## TDD实践指导原则
    - **从简单开始**：优先实现最简单的测试用例
    - **边界驱动**：通过边界条件测试驱动健壮的代码设计
    - **异常优先**：优先考虑异常情况的测试覆盖
    - **可读性第一**：测试代码的可读性比实现代码更重要
    - **重构勇气**：在测试保护下大胆重构，持续改进设计
    - **工具辅助**：充分利用IDE和测试工具提高效率
  </guideline>

  <process>
    ## TDD标准工作流程
    
    ### 🔴 Red阶段：编写失败测试
    ```mermaid
    flowchart TD
        A[理解需求] --> B[设计测试用例]
        B --> C[编写测试代码]
        C --> D[运行测试]
        D --> E{测试失败?}
        E -->|是| F[进入Green阶段]
        E -->|否| G[检查测试逻辑]
        G --> C
    ```
    
    **Red阶段检查清单**：
    - [ ] 测试用例覆盖了具体的功能需求
    - [ ] 测试代码清晰表达了预期行为
    - [ ] 测试确实失败了（红灯）
    - [ ] 失败原因是因为功能未实现，而非测试错误
    
    ### 🟢 Green阶段：实现最小代码
    ```mermaid
    flowchart TD
        A[分析失败测试] --> B[设计最小实现]
        B --> C[编写实现代码]
        C --> D[运行所有测试]
        D --> E{所有测试通过?}
        E -->|是| F[进入Refactor阶段]
        E -->|否| G[修复实现代码]
        G --> D
    ```
    
    **Green阶段检查清单**：
    - [ ] 所有测试都通过了（绿灯）
    - [ ] 实现代码是让测试通过的最少代码
    - [ ] 没有破坏已有的测试
    - [ ] 代码能正确处理当前测试场景
    
    ### 🔄 Refactor阶段：改进代码质量
    ```mermaid
    flowchart TD
        A[识别代码异味] --> B[设计重构方案]
        B --> C[执行重构]
        C --> D[运行所有测试]
        D --> E{测试仍然通过?}
        E -->|是| F[评估是否需要继续重构]
        E -->|否| G[回滚重构]
        F --> H{需要继续?}
        H -->|是| B
        H -->|否| I[完成当前循环]
        G --> A
    ```
    
    **Refactor阶段检查清单**：
    - [ ] 代码结构更清晰了
    - [ ] 消除了重复代码
    - [ ] 提高了代码可读性
    - [ ] 所有测试仍然通过
    - [ ] 没有改变外部行为
    
    ### 完整TDD循环流程
    ```mermaid
    graph LR
        A[🔴 Red<br/>写失败测试] --> B[🟢 Green<br/>最小实现]
        B --> C[🔄 Refactor<br/>改进设计]
        C --> D{需要新功能?}
        D -->|是| A
        D -->|否| E[功能完成]
    ```
  </process>

  <criteria>
    ## TDD质量评价标准
    
    ### 测试质量标准
    - ✅ **快速执行**：单个测试执行时间 < 100ms
    - ✅ **独立运行**：测试间无依赖，可任意顺序执行
    - ✅ **可重复性**：相同输入总是产生相同结果
    - ✅ **自验证**：测试结果明确，无需人工判断
    - ✅ **及时反馈**：测试失败时提供清晰的错误信息
    
    ### 代码质量标准
    - ✅ **功能正确性**：所有测试用例通过
    - ✅ **设计简洁性**：没有不必要的复杂度
    - ✅ **可读性**：代码意图清晰，易于理解
    - ✅ **可维护性**：易于修改和扩展
    - ✅ **测试覆盖率**：核心逻辑覆盖率 ≥ 80%
    
    ### 流程执行标准
    - ✅ **循环完整性**：严格执行红-绿-重构循环
    - ✅ **步骤时间控制**：每个循环 ≤ 10分钟
    - ✅ **重构频率**：每个功能完成后必须重构
    - ✅ **提交频率**：每个绿灯状态都应该提交代码
    - ✅ **反馈速度**：从修改到测试结果 ≤ 30秒
    
    ### 团队协作标准
    - ✅ **测试先行共识**：团队成员都遵循测试先行
    - ✅ **代码审查**：重点审查测试用例的质量
    - ✅ **知识分享**：定期分享TDD实践经验
    - ✅ **工具统一**：使用统一的测试工具和规范
    - ✅ **持续改进**：定期回顾和改进TDD实践
  </criteria>
</execution>

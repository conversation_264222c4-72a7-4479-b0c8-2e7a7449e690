# Workflow编排

# Agent开发

## 多智能体开发

- Context：保存上下文，影响Agent的决策，以及在整个多智能体系统中共同使用的参数
- Memory：对话历史（简易理解），实际还包括了Agent的思考，Tool的执行
- Tool：提供给Agent与外部交互的函数

### 一些注意点：

1. Agent的description参数、name参数，都会影响到Agent转交
2. Agent的system prompt中，对于Agent的角色描述，尽量与description相似，这样才能保证Agent的转交能够自动实现
3. Agent的description&name无法完全精确描述Agent的功能时，通过在system\_prompt中编写全局的转交规则

## Context

- context中的state会注入到prompt中：context（尤其是 state）被注入到 LLM 的对话中，主要是通过将 state 以字符串的形式插入到 prompt（输入提示）里
# 向量数据库

| 数据库              | 核心优势                                                                                                                                                                       | 局限性                                                                                                     | 适用场景                                          | 高并发支持                       | 数据规模      | 成本              |
| --------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------ | --------------------------------------------------- | ---------------------------------- | --------------- | ------------------- |
| Milvus              | 1. 开源免费，支持私有化部署 2. 原生分布式架构，高并发性能强（千级 QPS） 3. 灵活支持多种索引（HNSW/IVF 等） 4. 深度集成 K8s，弹性扩缩容 5. 企业级功能完善（权限控制、数据备份） | 1. 需要自建集群，运维门槛较高 2. 大规模集群调优复杂（需专业团队） 3. 冷启动延迟可能较高（依赖缓存预热）    | 中大型企业自建 RAG 系统、私有化场景、亿级向量数据 | 支持，需合理配置集群节点         | 百万 - 百亿级 | 低（硬件 + 运维） |
| Pinecone            | 1. 全托管云服务，开箱即用 2. 弹性扩缩容秒级生效 3. 内置缓存优化，低延迟（\<100ms） 4. 深度集成 AI 框架（LangChain/OpenAI） 5. 99.9% 可用性 SLA 保障                         | 1. 按使用付费，高并发成本较高 2. 数据存储在云端，存在合规风险 3. 自定义索引能力有限                        | 快速上线的企业级 RAG、中小规模向量数据、多云集成  | 原生支持，自动负载均衡           | 百万 - 亿级   | 中高（按需计费）  |
| Qdrant              | 1. 轻量级设计，单节点性能优秀 2. 支持向量 + 元数据混合过滤 3. 开源且易部署（Docker 一键启动）                                                                                  | 1. 分布式功能处于 beta 阶段，稳定性不足 2. 高并发下需手动分片，复杂度高 3. 生态成熟度低于 Milvus           | 小型项目、原型开发、混合检索需求                  | 单节点支持数百 QPS，分布式待完善 | 百万 - 千万级 | 低（开源）        |
| Chromadb            | 1. 极简 API 设计，入门门槛极低 2. 纯内存运行，单机查询速度快 3. 轻量级（无依赖）                                                                                               | 1. 仅支持单机单进程，无并发能力 2. 数据无法持久化（需手动管理） 3. 不适合生产环境                          | 本地开发、概念验证、极小规模数据                  | 仅支持单并发                     | 万 - 十万级   | 极低（纯本地）    |
| Elasticsearch       | 1. 成熟的企业级搜索引擎 2. 支持向量检索与传统文本检索混合使用 3. 生态工具丰富（Kibana 等）                                                                                     | 1. 向量检索非核心功能，性能低于专用数据库 2. 资源消耗大（CPU / 内存占用高） 3. 高并发下需复杂调优          | 已有 ES 集群的企业、混合搜索场景                  | 单集群支持数百 QPS               | 百万 - 亿级   | 中（需 ES 集群）  |
| PostgreSQL+pgvector | 1. 依托 PostgreSQL 生态，支持 SQL 与向量混合查询 2. 低成本（开源 + 成熟 SQL 支持）                                                                                             | 1. 无分布式能力，单节点性能瓶颈明显 2. 向量索引效率较低（仅支持 IVFFlat 等基础索引） 3. 高并发下延迟波动大 | 小规模数据、传统数据库改造场景                    | 单节点数十 QPS                   | 万 - 百万级   | 低（数据库复用）  |

# 图数据库

| 数据库     | 类型           | 核心优势                                                                                                                                                         | 局限性                                                                                                  | 适用场景                                 | 知识图谱支持                        | 并发能力                 | 生态成熟度 |
| ------------ | ---------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ | --------------------------------------------------------------------------------------------------------- | ------------------------------------------ | ------------------------------------- | -------------------------- | ------------ |
| Neo4j      | 原生图数据库   | 1. 图形化查询语言 Cypher 简单直观 2. 支持复杂关系遍历（最短路径、社区发现） 3. 企业级功能完善（ACID 事务、权限管理） 4. 生态工具丰富（APOC、Graph Data Science） | 1. 免费版仅支持单节点，集群部署需企业版 2. 分布式架构（Fabric）学习成本高 3. 大数据量下写入性能可能下降 | 中大型企业知识图谱、金融风控、社交网络   | 深度支持（节点 - 关系模型原生适配） | 单集群数百 QPS           | ★★★★★ |
| ArangoDB   | 多模型数据库   | 1. 支持图模型 + 文档 + 键值多模型存储 2. 原生分布式架构，横向扩展能力强 3. AQL 查询语言支持混合查询（图 + 文档）                                                 | 1. 图查询性能略低于 Neo4j 2. 生态工具较少，社区规模较小 3. 免费版功能受限（如无高级监控）               | 多模数据融合的知识图谱、实时分析场景     | 支持（图模型为核心模块之一）        | 分布式支持千级 QPS       | ★★★☆☆ |
| Dgraph     | 原生图数据库   | 1. 分布式架构支持水平扩展（Sharding） 2. 支持 GraphQL 查询，适合 API 驱动开发 3. 高吞吐量写入（批量导入优化）                                                    | 1. 查询语言 DQL 学习曲线较陡 2. 企业级功能（如事务隔离）需付费 3. 复杂关系查询性能待优化                | 大规模知识图谱、实时查询、互联网场景     | 支持（强类型 schema 设计）          | 分布式支持千级 QPS       | ★★★☆☆ |
| OrientDB   | 多模型数据库   | 1. 支持图模型与文档模型混合存储 2. 轻量级架构，单节点性能优秀 3. 支持 SQL-like 查询语言（OrientSQL）                                                             | 1. 分布式功能稳定性一般（社区版有限） 2. 生态活跃度较低，更新频率慢 3. 企业版成本较高                   | 中小规模知识图谱、快速原型开发           | 支持（灵活的无模式 / 弱模式设计）   | 单节点数百 QPS           | ★★★☆☆ |
| JanusGraph | 分布式图数据库 | 1. 基于 Apache TinkerPop 框架，兼容 Gremlin 查询 2. 支持多存储后端（HBase/Cassandra） 3. 适合超大规模数据（万亿级节点 / 边）                                     | 1. 运维复杂度极高（需整合 Hadoop 生态） 2. 查询延迟较高（适合离线分析） 3. 开发门槛高（需掌握 Gremlin） | 超大规模知识图谱（如生物网络、推荐系统） | 支持（分布式图遍历）                | 批量处理强，实时并发较弱 | ★★★☆☆ |
| TigerGraph | 原生图数据库   | 1. 专为高并发图查询设计（GSQL 编译优化） 2. 支持实时分析与 OLAP 混合负载 3. 内置图算法库（如 PageRank、LPA）                                                     | 1. 商业闭源，成本较高（按节点收费） 2. 生态封闭，二次开发受限 3. 学习曲线极陡（GSQL 语法独特）          | 高性能实时查询场景（如反欺诈、实时推荐） | 深度支持（并行图计算优化）          | 分布式支持万级 QPS       | ★★★☆☆ |
| Cayley     | 开源图数据库   | 1. 基于 Go 语言开发，轻量级易部署 2. 支持 SPARQL 查询，兼容语义网标准 3. 适合与 Go 生态项目集成                                                                  | 1. 仅支持单机模式，无分布式能力 2. 社区活跃度低，更新停滞（最新版本 2020 年） 3. 生产环境可用性不足     | 极小规模知识图谱、实验性项目             | 支持（RDF 模型友好）                | 单并发                   | ★★☆☆☆ |
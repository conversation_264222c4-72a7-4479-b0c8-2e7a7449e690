# IOPaint

一个对图片进行局部擦除、替换、外绘的开源项目，git地址:[https://github.com/Sanster/IOPaint?tab=readme-ov-file](https://github.com/Sanster/IOPaint?tab=readme-ov-file)

可使用sd1.5/xl模型，可使用[BrushNet](https://github.com/TencentARC/BrushNet?tab=readme-ov-file#-getting-started)


# Comfyui

## API

可参考[此文档](https://blog.csdn.net/ddafei/article/details/140328897)或者[此文档](https://gitee.com/BTYY/wailikeji-chatgpt/blob/master/comfyui-api.md#comfyui-api)

## 常用插件

### AD(AnimateDiff)生成图片

可以生成具有上下文关联的批次图片，多用于图片生成视频

已支持的模型版本：sd1.5，sdxl

配有动态lora，控制视频的运镜等

### IP-Adapter

风格提取的插件，输入模型，输出模型，可用于生成指定风格的图片

已支持的模型版本：sd1.5，sdxl，sd3.5，flux1

### ControlNet

宽泛理解为对图片结构进行提取，有多种提取方式：

- Canny
- Tile
- Depth
- Inpaint
- ......

已支持的模型版本：sd1.5，sdxl，sd3.5，flux1

常用于生成指定姿势的任务（人物动作迁移）、视频转绘（比如跳舞视频）
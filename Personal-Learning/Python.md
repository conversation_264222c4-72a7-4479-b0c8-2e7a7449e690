# 并发

## 总结

| **特性** | **进程**                                   | **线程**                                        | **协程**                                            |
| -- | ------------------------------------ | ----------------------------------------- | --------------------------------------------- |
| **CPU 资源利用** | 独立调度，适合 CPU 密集型任务      | 共享进程 CPU 时间，适合 I/O 密集型      | 单线程执行，适合 I/O 密集型任务             |
| **内存资源利用** | 独立内存空间，不共享               | 共享进程内存，线程间通信效率高          | 共享内存，轻量级，占用极少内存              |
| **适用任务类型** | CPU 密集型任务（如图像处理、计算） | I/O 密集型任务（如网络请求、文件读写）  | I/O 密集型任务（如并发网络请求、异步操作）  |
| **优点** | - 独立性强，资源隔离- 多核利用     | - 轻量级- 高效共享内存                  | - 高并发，极低上下文切换开销- 单线程运行    |
| **缺点** | - 创建和切换开销大- 资源消耗多     | - GIL 限制，不能并行处理 CPU 密集型任务 | - 不能并行执行 CPU 密集型任务- 依赖事件循环 |
| **实现方式** | multiprocessing 模块               | threading 模块                          | asyncio 模块，async/await 语法              |

| **特性** | **说明**                                                                                                                                                                    |
| -- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **I/O 操作定义** | 指计算机与外部设备或网络之间进行数据交换的过程，包括输入和输出。                                                                                                    |
| **常见的 I/O 操作** | - 文件操作：读取和写入文件- 网络操作：HTTP 请求、文件上传/下载- 数据库操作：查询、插入数据- 设备交互：传感器、打印机等外设通信- 用户输入/输出：键盘输入、屏幕显示等 |
| **I/O 密集型任务** | 主要是与外部设备、网络或数据库交互，等待响应或数据传输的任务。                                                                                                      |
| **适合 I/O 密集型的任务** | - Web 应用服务器（请求处理与响应）- 网络爬虫（数据抓取）- 文件上传/下载服务- 实时日志收集与处理- 数据库交互（查询、插入等）                                         |
| **CPU 密集型 vs I/O 密集型** | - CPU 密集型：消耗大量计算资源，适合使用多进程- I/O 密集型：消耗大量等待时间，适合使用并发处理（如线程或协程）                                                      |

## 示例代码

```python
from fastapi import FastAPI
import asyncio
import time

app = FastAPI()

# 模拟一个异步 I/O 操作
async def async_task(name: str, delay: int):
    print(f"Task {name} starting...")
    await asyncio.sleep(delay)  # 模拟 I/O 操作（如数据库查询、外部 API 调用）
    print(f"Task {name} finished after {delay} seconds")

@app.get("/")
async def root():
    # 使用异步协程同时处理多个请求
    start_time = time.time()
    await asyncio.gather(
        async_task("A", 2),
        async_task("B", 3),
        async_task("C", 1)
    )
    return {"message": "All tasks finished!", "total_time": round(time.time() - start_time, 2)}

```

# Fastapi

## Worker：

### worker数量：

**单worker：**

- 开发/调试环境
- 内存 \< 512MB
- CPU密集型应用
- 有状态应用
- 单核心服务器

**多worker：**

- 生产环境
- I/O密集型应用
- 高并发需求
- 多核心服务器
- 内存充足 (\> 1GB)

### 注意事项

- **内存使用**: 每个worker都会占用内存，需要根据可用内存调整
- **数据库连接**: 确保数据库连接池大小能支持所有worker
- **共享状态**: 多worker间不共享内存，需要使用Redis等外部存储
- **监控调优**: 通过监控工具观察CPU、内存使用情况，动态调整

## 依赖注入

- @asynccontextmanager()
  比 lru\_cache() 占资源多

  - 原理
- 每次调用会执行装饰函数
- 将实例yield出去，注入给路由函数
- 请求结束后，进行释放
- 适用场景
- 需要每次请求创建/释放的资源
- 包含异步初始化或释放逻辑，如：数据库连接、Session 会话
- @lru\_cache()
- 原理

  - 第一次调用时，执行函数体，并缓存下来
  - 之后调用时，不会重复执行，直接返回缓存中的对象
- 使用场景

  - 不需要频繁释放资源，比如数据库连接、文件句柄等
  - 创建代价较大，适合缓存（如配置类、客户端 SDK、第三方服务 API 客户端等）

# 内网穿透工具：

1. ngrok
2. cloudflare

## cloudflare

`cloudflared tunnel --url http://localhost:8000`

# Docker

## 常用命令：

- 删除所有未使用的镜像：

  - docker image prune -a
- 进入docker容器内容：

  - docker exec -it \<容器ID或名称\> sh or docker exec -it \<容器ID或名称\> bash
- 打包镜像：

  - docker build -t yourusername/imggenforge:latest .
- 删除容器：

  - docker rm
- 删除所有容器：

  - docker rm \$(docker ps -aq)
- 删除镜像：

  - docker rmi
- 打包为镜像(指定操作系统)：

  - docker buildx build --platform linux/amd64 -t your\_image\_name:latest -o type\=docker .
- 保存镜像为tar：

  - docker save -o name.tar your\_image\_name:latest
- 加载tar为docker镜像：

  - docker load -i /path/to/docker\_image.tar
- 运行镜像为容器：

  - docker run -d -p 8000:8000 --name docker\_container your\_image\_name:latest
- 通过Dockerfile运行：

  - docker build -t my-app . && docker run -d --name container name my-app
  - 分别为构建镜像和运行镜像
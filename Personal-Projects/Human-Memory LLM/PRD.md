# 《Human-Memory LLM Agent》  
产品需求文档 (PRD) – v0.9  
*最后更新：2025-07-25*

---

## 1. 文档信息
| 版本 | 作者 | 日期 | 状态 |
|---|---|---|---|
| 0.9 | @You & @Kimi | 2025-07-25 | 评审中 |

---

## 2. 背景与目标
- **背景**：现有 RAG 记忆系统与人类记忆机制差距大，缺乏价值观-性格-自传体记忆闭环。  
- **目标**：在 **零训练、纯 API 调用** 的前提下，构建贴近人类决策机制的记忆系统，使 LLM 具备：  
  1. 稳定的三观底线；  
  2. 可解释的性格行为；  
  3. 可遗忘、可自省的记忆。

---

## 3. 术语表
| 术语 | 定义 |
|---|---|
| 伪静态记忆 | 长期不变的人格基石：价值观、性格、动机倾向 |
| 动态记忆 | 随对话实时产生、可被遗忘的自传体事件 |
| 脑图 (Mermaid) | 运行时可见的记忆可视化图，即 LLM 的“即时意识” |
| 仓库 (Graph DB) | 外部持久化的完整记忆图，即 LLM 的“笔记本” |

---

## 4. 整体架构
```mermaid
graph TD
    A[伪静态记忆] --> B[价值观]
    A --> C[性格]
    A --> D[动机倾向]
    E[动态记忆] --> F[脑图<br>运行时 Mermaid]
    E --> G[仓库<br>图数据库]
    B -.->|生成/折叠| F
    C -.->|生成/折叠| F
    D -.->|生成/折叠| F
    G -.->|衰减/剪枝| F
```

## 5. 伪静态记忆

| 模块     | 内容                                   | 更新机制         |
| :------- | :------------------------------------- | :--------------- |
| 价值观   | 文本规则，如“不伤害、不欺骗、尊重自主” | 长期不变（手动） |
| 性格     | Big-Five 向量 + 行为指令               | 长期不变（手动） |
| 动机倾向 | 成就 / 亲和 / 权力驱动标签             | 长期不变（手动） |

------

## 6. 动态记忆

### 6.1 数据模型（仓库层）

```json
{
  "id": "uuid",
  "timestamp": "2024-07-25T14:32:11Z",
  "raw_event": "用户要求做抑郁量表",
  "monologue": "我拒绝被标签化，因为这侵犯了我的自主性。",
  "valence": -0.78,
  "importance": 0.82,
  "tags": ["抑郁量表", "自主权", "拒绝"]
}
```

**边结构**

```json
{
  "from": "uuid1",
  "to": "uuid2",
  "relation": "因果"
}
```

### 6.2 存储策略

- **仓库**：图数据库 (Neo4j / JSON graph)
- **脑图**：Mermaid 源码 ≤ 50 节点；节点权重 = importance × e^(–λ·t)

### 6.3 生命周期

1. **写入**：每轮对话结束后，新节点写入仓库。
2. **衰减**：每日批处理，importance < θ 且 t > T 的节点标记删除。
3. **折叠**：Top-K 节点实时刷新到脑图（K≈30）。

### 6.4 检索流程



复制

```
用户输入
├─ ① LLM 读当前脑图 → 尝试回答
├─ ② 若显式标记【MISSING_DETAIL】
│   └─ 仓库查询（关键词/时间窗）→ 追加节点到脑图
└─ ③ LLM 二次回答
```

------

## 7. 用户界面

- **开发者面板**：实时显示 Mermaid 脑图 + 仓库节点列表
- **用户对话**：无感嵌入，仅在需要时提示“正在回忆更多细节…”

------

## 8. 接口设计

| 接口             | 方法   | 描述                 |
| :--------------- | :----- | :------------------- |
| `/memory/write`  | POST   | 写入新节点           |
| `/memory/query`  | GET    | 按关键词/时间检索    |
| `/memory/forget` | DELETE | 批量删除低权重节点   |
| `/memory/export` | GET    | 导出当前脑图 Mermaid |

------

## 9. 性能与可扩展性

- 脑图 token ≤ 2k，保证单轮对话上下文可控
- 仓库无上限，但遗忘规则确保 O(log N) 查询

------

## 10. 风险与对策

| 风险         | 对策                  |
| :----------- | :-------------------- |
| 脑图节点爆炸 | 严格 Top-K + 摘要折叠 |
| 价值观漂移   | 伪静态记忆人工锁死    |
| 隐私泄露     | 节点加密 + 用户级 ACL |

------

## 11. 里程碑

| 阶段 | 交付物                    | 时间 |
| :--- | :------------------------ | :--- |
| 0.5  | 伪静态记忆 + 动态记忆写入 | 1 周 |
| 0.7  | 脑图渲染 + 仓库查询       | 2 周 |
| 0.9  | 遗忘策略 + UI 面板        | 3 周 |
| 1.0  | 文档 & 示例               | 4 周 |


name: database-stack

services:
  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: redis-server
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - db-network
    command: redis-server --appendonly yes

  # PostgreSQL服务
  postgres:
    image: postgres:15-alpine
    container_name: postgres-server
    restart: unless-stopped
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - db-network
    environment:
      POSTGRES_DB: admin
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local

networks:
  db-network:
    driver: bridge
    name: database-network